export default {

    'self.assessment.details.select.knowledge': '<PERSON><PERSON> pengetahuan: ',
    'self.assessment.details.select.knowledge.placeholder': 'Silakan pilih basis pengetahuan',
    'self.assessment.details.notification.channels': 'Saluran sumber: ',
    'self.assessment.details.notification.channels.placeholder': 'Silakan pilih saluran sumber',
    'self.assessment.details.ask.customer': 'Pelanggan yang mengajukan pertanyaan: ',
    'self.assessment.details.ask.customer.placeholder': '<PERSON>lakan masukkan nama pelanggan, email pelanggan, atau nomor telepon pelanggan',
    'self.assessment.details.ask.customer.placeholder1': 'Silakan masukkan email pelanggan',
    'self.assessment.details.ask.customer.placeholder.phone': 'Silakan masukkan nomor telepon pelanggan',
    'self.assessment.details.customer.issues': "Pertanyaan pelanggan: ",
    'self.assessment.details.customer.issues.placeholder': '<PERSON>lakan masukkan pertanyaan <PERSON>',
    'self.assessment.details.question.time': '<PERSON><PERSON><PERSON> pertany<PERSON>: ',
    'self.assessment.details.title': '<PERSON><PERSON><PERSON>',
    'self.assessment.details.table.assessment.type': '<PERSON><PERSON> eval<PERSON>',
    'self.assessment.details.table.manual.evaluation': 'Manual',
    'self.assessment.details.table.automatic.evaluation': 'Otomatis',
    'self.assessment.details.table.customer.issues': "Pertanyaan pelanggan",
    'self.assessment.details.table.answer.obtained': "Jawaban Chatbot AI",
    'self.assessment.details.table.average.accuracy.score': 'Skor akurasi rata-rata',
    'self.assessment.details.table.loyalty.score': 'Skor Kedekatan dengan Fakta',
    'self.assessment.details.table.answer.relevance.score': 'Skor Relevansi Jawaban',
    'self.assessment.details.table.context.accuracy.score': 'Skor Presisi Konteks',
    'self.assessment.details.table.semantic.similarity.score': 'Skor Kesamaan Semantik Jawaban',
    'self.assessment.details.table.correct.answer.score': 'Skor Ketepatan Jawaban',
    'self.assessment.details.table.ask.customer': 'Pelanggan',
    'self.assessment.details.table.interaction.time': 'Waktu pertanyaan',
    'self.assessment.details.table.knowledge.name': 'Nama basis pengetahuan',
    'self.assessment.details.table.loyalty.score.tips.text': 'Ini mengukur konsistensi faktual jawaban yang dihasilkan terhadap konteks yang diberikan. Dihitung dari jawaban dan konteks yang diambil. Jawaban diskalakan dalam rentang (0,100). Semakin tinggi semakin baik.',
    'self.assessment.details.table.loyalty.score.tips.text.1': 'Jawaban dianggap setia jika semua klaim dalam jawaban dapat disimpulkan dari konteks yang diberikan.',
    'self.assessment.details.table.answer.relevance.score.tips.text': 'Metrik evaluasi, Relevansi Jawaban, berfokus pada penilaian seberapa penting jawaban yang dihasilkan terhadap prompt yang diberikan. Skor yang lebih rendah diberikan untuk jawaban yang tidak lengkap atau mengandung informasi berlebihan dan skor yang lebih tinggi menunjukkan relevansi yang lebih baik. Metrik ini dihitung menggunakan pertanyaan, konteks, dan jawaban.',
    'self.assessment.details.table.context.accuracy.score.tips.text': 'Presisi Konteks adalah metrik yang mengevaluasi apakah semua item yang relevan dalam konteks berada di peringkat teratas atau tidak. Idealnya, semua potongan yang relevan harus muncul di peringkat teratas. Metrik ini dihitung menggunakan pertanyaan, kebenaran dasar, dan konteks, dengan nilai antara 0 dan 100, di mana skor yang lebih tinggi menunjukkan presisi yang lebih baik.',
    'self.assessment.details.table.semantic.similarity.score.tips.text': 'Konsep Kesamaan Semantik Jawaban berkaitan dengan evaluasi kesamaan semantik antara jawaban yang dihasilkan dan kebenaran dasar. Evaluasi ini didasarkan pada kebenaran dasar dan jawaban, dengan nilai antara 0 hingga 100. Skor yang lebih tinggi menandakan keselarasan yang lebih baik antara jawaban yang dihasilkan dan kebenaran dasar.',
    'self.assessment.details.table.correct.answer.score.tips.text': 'Penilaian Ketepatan Jawaban melibatkan pengukuran akurasi jawaban yang dihasilkan dibandingkan dengan kebenaran dasar. Evaluasi ini bergantung pada kebenaran dasar dan jawaban, dengan skor antara 0 hingga 100. Skor yang lebih tinggi menunjukkan keselarasan yang lebih dekat antara jawaban yang dihasilkan dan kebenaran dasar, menandakan ketepatan yang lebih baik.',
    'self.assessment.details.table.correct.answer.score.tips.text.1': 'Ketepatan Jawaban mencakup dua aspek kritis: kesamaan semantik antara jawaban yang dihasilkan dan kebenaran dasar, serta kesamaan faktual. Aspek-aspek ini digabungkan menggunakan skema pembobotan untuk membentuk skor ketepatan jawaban.',
    'self.assessment.details.title.left': 'Pertanyaan',
    'self.assessment.details.title.right': 'Skor evaluasi',
    'self.assessment.details.evaluation.time': 'Waktu evaluasi: ',
    'self.assessment.details.comparison.answers': 'Perbandingan jawaban',
    'self.assessment.details.document.fragments': 'Potongan dokumen asli',
    'self.assessment.details.robot.answer': "Jawaban Chatbot AI",
    'self.assessment.details.correct.answer': 'Jawaban yang benar',
    'self.assessment.details.add.faq': 'Tambahkan ke FAQ',
    'self.assessment.details.document.knowledge': 'Basis pengetahuan AlGC',
    'self.assessment.details.input.correct.answer': 'Masukkan jawaban yang benar secara manual',
    'self.assessment.details.create.correct.answer.text': 'Setelah memasukkan jawaban yang benar secara manual, sistem dapat mengevaluasi nilai "Presisi konteks", "Kesamaan semantik jawaban", dan "Ketepatan jawaban" untuk Anda.',
    'self.assessment.details.input.correct.answer.placeholder': 'Silakan masukkan jawaban yang benar.',
    'self.assessment.details.start.evaluating': 'Mulai evaluasi',
    'self.assessment.details.cancel.evaluation': 'Batalkan evaluasi',
    'self.assessment.details.loyalty': 'Kedekatan dengan Fakta',
    'self.assessment.details.answer.relevance': 'Relevansi jawaban',
    'self.assessment.details.context.accuracy': 'Presisi konteks',
    'self.assessment.details.semantic.similarity.answers': 'Kesamaan jawaban',
    'self.assessment.details.correct.answer.radar': 'Ketepatan jawaban',
    'self.assessment.details.handle.input.text': 'Setelah memasukkan jawaban yang benar secara manual, sistem dapat mengevaluasi nilai "Presisi konteks", "Kesamaan semantik jawaban", dan "Ketepatan jawaban" untuk Anda.',
    'self.assessment.details.title.right.evaluation.status': 'Sedang dievaluasi...',
    // 智能客服对话洞察
    'self.assessment.details.interactivity.type': 'Jenis interaksi: ',
    'customer.service.conversation.insights.answer.type.select': 'Jenis jawaban: ',
    'customer.service.conversation.insights.answer.type.select.placeholder': 'Silakan pilih jenis jawaban',
    'customer.service.conversation.insights.like': 'Suka',
    'customer.service.conversation.insights.downvote': 'Tidak suka',
    'self.assessment.details.table.interactivity.type': 'Jenis interaksi',
    'self.assessment.details.document.fragments.faq': 'Jawaban FAQ',
    'self.assessment.details.document.fragments.answer': 'Jawaban',
    'customer.service.conversation.insights.no.data.tips': 'Kurang pengetahuan yang relevan, silakan pelihara basis pengetahuan',
    'customer.service.conversation.insights.no.data.tips.1': 'Anda saat ini tidak memiliki informasi data apa pun...',
    'customer.service.conversation.insights.title': 'Wawasan Percakapan Chatbot AlGC',
    'customer.service.conversation.insights.from.faq': 'Dari basis pengetahuan FAQ',
    'customer.service.conversation.insights.input.correct.answer': 'Masukkan jawaban yang benar',
    'customer.service.conversation.insights.answer.type': 'Jenis jawaban',
    'customer.service.conversation.insights.table.knowledge.QA': 'Jawaban Normal FAQ',
    'customer.service.conversation.insights.table.lack.knowledge': 'RAG Kurang Pengetahuan',
    'customer.service.conversation.insights.table.ai.dont.know': 'RAG Tidak Diketahui',
    'customer.service.conversation.insights.table.aigc.answer': 'Jawaban Normal RAG',
    'customer.service.conversation.insights.table.aigc.answer.aigc': 'Jawaban Normal AIGC',
    'customer.service.conversation.insights.title.right': 'Evaluasi akurasi',
    'customer.service.conversation.insights.detail.answer.null': 'Belum ditemukan jawaban.',
    'self.assessment.details.title.problem.rewriting': 'Penulisan ulang pertanyaan: ',
    // 智能客服热点问题分析
    'customer.service.hot.topic.analysis.title': 'Analisis Pertanyaan Populer Chatbot AIGC',
    'customer.service.hot.topic.analysis.top.20': 'Awan kata 20 teratas',
    'customer.service.hot.topic.analysis.top.100': 'Awan kata 100 teratas',
    'customer.service.hot.topic.analysis.search.tips': 'Silakan masukkan tag untuk mencari',
    'customer.service.hot.topic.analysis.table.question.tag': 'Awan kata',
    'customer.service.hot.topic.analysis.top.10': '10 Pertanyaan yang Paling Sering Ditanyakan',
    'customer.service.hot.topic.analysis.table.ranking': 'Peringkat',
    'customer.service.hot.topic.analysis.table.ask.questions': 'Ajukan pertanyaan',
    'customer.service.hot.topic.analysis.table.ask.number': 'Jumlah pertanyaan yang diajukan',
    'customer.service.hot.topic.analysis.table.like.number': 'Jumlah suka',
    'customer.service.hot.topic.analysis.table.dislike.number': 'Jumlah tidak suka',
    'customer.service.hot.topic.analysis.table.recent.question.time': 'Waktu pertanyaan terbaru',
    'customer.service.hot.topic.analysis.top.20.tips': 'Menampilkan awan kata 20 teratas yang paling sering ditanyakan. Tag ini memberikan gambaran cepat tentang area utama perhatian pengguna.',
    'customer.service.hot.topic.analysis.top.100.tops': 'Menampilkan awan kata 100 teratas yang paling sering ditanyakan. Dengan mengklik tag, Anda dapat mengambil data detail tentang pertanyaan di bawah tag tersebut. Memahami tag ini membantu mengidentifikasi masalah yang paling difokuskan pengguna dan memberikan dukungan data untuk mengoptimalkan basis pengetahuan dan meningkatkan layanan pelanggan.',
    'customer.service.hot.topic.analysis.top.10.tips': 'Menunjukkan 10 pertanyaan yang paling sering ditanyakan oleh pengguna. Frekuensi tinggi pertanyaan ini menunjukkan bahwa mereka adalah topik yang paling menjadi perhatian pengguna.',
    // 缺乏知识问题分析
    'lack.knowledge.problem.analysis.title': 'Analisis Pertanyaan Kurang Pengetahuan',
    'lack.knowledge.problem.analysis.top.20': 'Awan kata 20 teratas karena kurang pengetahuan',
    'lack.knowledge.problem.analysis.top.100': 'Awan kata 100 teratas kurang pengetahuan',
    'lack.knowledge.problem.analysis.bottom.title': 'Pertanyaan yang dijawab sebagai "Saya tidak tahu" karena kurang pengetahuan',
    'lack.knowledge.problem.analysis.top.20.tips': 'Menampilkan awan kata 20 teratas yang menghasilkan respons "kurang pengetahuan" setelah pertanyaan pelanggan. Tag ini mewakili area di mana pengguna sering mengalami masalah, menunjukkan kebutuhan mendesak untuk melengkapi dan meningkatkan konten basis pengetahuan.',
    'lack.knowledge.problem.analysis.top.100.tips': 'Menampilkan awan kata 100 teratas yang menghasilkan respons "kurang pengetahuan" setelah pertanyaan pelanggan. Memahami tag ini membantu mengidentifikasi masalah yang paling difokuskan pengguna dan memberikan dukungan data untuk mengoptimalkan basis pengetahuan dan meningkatkan layanan pelanggan.',
    'lack.knowledge.problem.analysis.bottom.title.tips': 'Menunjukkan semua pertanyaan yang menerima respons "kurang pengetahuan".',
};
