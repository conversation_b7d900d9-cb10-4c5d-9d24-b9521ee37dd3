export default {
    'intelligent.form.filling.title': 'Pengisian formulir cerdas',
    'intelligent.form.filling.btn.add': 'Konfigurasi pengisian formulir cerdas',
    'intelligent.form.filling.table.language': '<PERSON>hasa',
    'intelligent.form.filling.table.attribute.name': '<PERSON><PERSON> atribut',
    'intelligent.form.filling.table.create.time': '<PERSON><PERSON>tu pembuatan',
    'intelligent.form.filling.table.modify.time': 'Waktu modifikasi',
    'contact.customers.title.configure.properties': 'Konfigurasi atribut',
    'contact.customers.form.attribute.name': '<PERSON>a atribut:',
    'contact.customers.form.attribute.name.required': 'Masukkan nama atribut',
    'contact.customers.form.attribute.example.values': 'Contoh nilai atribut:',
    'contact.customers.form.attribute.example.values.required': 'Masukkan contoh nilai atribut',
    'contact.customers.form.attribute.example.values.placeholder': '<PERSON><PERSON>kkan contoh nilai (misalnya, nomor pesanan). Ini membantu ekstraksi AIGC.',
    'contact.customers.form.attribute.description': 'Format properti:',
    'contact.customers.form.attribute.description.required': 'Masukkan format properti',
    'contact.customers.form.attribute.description.placeholder': 'Jelaskan karakteristik format atribut (misalnya, "Nomor pesanan terdiri dari 16 digit"). Ini membantu ekstraksi AIGC.',
    'contact.customers.form.save.attribute': 'Simpan atribut',
    'contact.customers.form.store.attribute': 'Simpan atribut ke:',
    'contact.customers.form.store.attribute.required': 'Pilih tujuan: Tiket atau Profil pelanggan',
    'contact.customers.form.ticket': 'Tiket',
    'contact.customers.form.customer': 'Profil pelanggan',
    'contact.customers.form.select.attribute': 'Pilih atribut:',
    'contact.customers.form.select.attribute.required': 'Pilih atribut',
    'contact.customers.form.add.attribute': 'Tambah atribut',
    'contact.customers.title.trigger.intelligent.agent': 'Aktifkan bot',
    'intelligent.form.filling.form.select.intelligent.agent': 'Pilih bot:',
    'intelligent.form.filling.form.select.intelligent.agent.placeholder': 'Pilih bot',
    'intelligent.form.filling.worktable.customer.name': 'Nama pelanggan:',
    'intelligent.form.filling.worktable.customer.name.required': 'Masukkan nama pelanggan',
    'intelligent.form.filling.worktable.phone.number': 'Nomor ponsel:',
    'intelligent.form.filling.worktable.phone.number.required': 'Masukkan nomor ponsel',
    'intelligent.form.filling.worktable.order.id': 'ID Pesanan',
    'intelligent.form.filling.worktable.order.id.required': 'Masukkan ID Pesanan',
    'intelligent.form.filling.worktable.effective.content.tips': 'Tidak ada konten valid untuk pengisian formulir cerdas',
    'intelligent.form.filling.ticket.type.default': 'Default',
};
