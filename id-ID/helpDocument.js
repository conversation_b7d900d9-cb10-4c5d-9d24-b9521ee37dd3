export default {
  'help.document.title': 'Dokumentasi Bantuan',
  'help.document.whats.app.title':
    'Cara mendaftarkan akun bisnis WhatsApp',
  'help.document.line.title': 'Cara mendaftarkan saluran Line',
  'help.document.we.chat.official.title':
    'Cara mengonfigurasi informasi saluran akun resmi WeChat',
  // Dokumentasi bantuan Line
  'help.document.line.left.menu': 'Buat akun resmi Line',
  'help.document.line.left.menu.1':
    'Dapatkan informasi konfigurasi saluran Line',
  'help.document.line.left.menu.2': 'Integrasikan Line ke ConnectNow',
  'help.document.line.step.1.title': 'Langkah 1:',
  'help.document.line.step.2.title': 'Langkah 2:',
  'help.document.line.step.3.title': 'Langkah 3:',
  'help.document.line.step.4.title': 'Langkah 4:',
  'help.document.line.step.5.title': '<PERSON>kah 5:',
  'help.document.line.step.6.title': '<PERSON>kah 6:',
  'help.document.line.step.7.title': '<PERSON>kah 7:',
  'help.document.line.step.9.title': '<PERSON>kah 8:',
  'help.document.line.step.10.title': 'Langkah 9:',
  'help.document.line.step.1.text.1':
    'Buka tautan: <a>https://tw.linebiz.com/account/</a>, klik "buat akun gratis"',
  'help.document.line.step.1.text.2':
    'Pilih untuk masuk dengan akun Line atau akun bisnis',
  'help.document.line.step.1.text.3':
    'Jika tidak yakin mana yang harus digunakan, silakan merujuk pada penjelasan di situs resmi Line:<a>"apa perbedaan antara masuk dengan akun Line dan masuk dengan akun bisnis?"</a>',
  'help.document.line.step.1.text.4':
    'Setelah masuk, isi informasi akun resmi Line, tinjau "persyaratan layanan akun resmi Line" dan kemudian klik "konfirmasi"',
  'help.document.line.step.1.text.5':
    'Setelah mengonfirmasi bahwa konten yang dimasukkan sudah benar, klik "kirim"',
  'help.document.line.step.1.text.6':
    'Jika layar berikut ditampilkan, artinya aplikasi akun resmi Line berhasil',
  'help.document.line.step.2.text':
    'Jika Anda sudah memiliki akun resmi Line, klik <a>manajer akun resmi Line</a> untuk masuk',
  'help.document.line.step.2.text.1':
    'Setelah masuk ke "manajer akun resmi Line", pertama pilih akun resmi Line untuk diintegrasikan:',
  'help.document.line.step.2.text.2': 'Klik "pengaturan"',
  'help.document.line.step.2.text.3':
    'Pilih "API pesan" di sebelah kiri, kemudian di halaman API pesan ini, klik "aktifkan API pesan"',
  'help.document.line.step.2.text.4':
    'Setelah mengklik "aktifkan API pesan", Anda pertama harus memilih penyedia layanan yang ada, atau membuat penyedia layanan baru (penyedia). Setelah pemilihan, silakan baca "persyaratan layanan API akun resmi Line" dengan saksama dan kemudian klik "setuju".',
  'help.document.line.step.2.text.5':
    'Selanjutnya, isi "URL untuk kebijakan privasi dan persyaratan layanan (opsional)". Jika tidak diisi, Anda masih dapat mengklik "konfirmasi" untuk melanjutkan ke langkah berikutnya',
  'help.document.line.step.2.text.6':
    'Setelah mengonfirmasi bahwa "nama akun" dan "nama penyedia layanan" sudah benar, klik "konfirmasi" untuk secara resmi mengaktifkan API pesan',
  'help.document.line.step.2.text.7':
    'Setelah mengklik "konfirmasi", layar berikut akan muncul dan statusnya akan menjadi "digunakan"',
  'help.document.line.step.2.text.8':
    'Selanjutnya, Anda dapat mengklik "pengembang Line" untuk mendapatkan lebih banyak data yang diperlukan untuk integrasi!',
  'help.document.line.step.2.text.9':
    'Untuk mengintegrasikan Line ke backend ConnectNow, Anda akan memerlukan 5 data berikut:',
  'help.document.line.step.2.text.10': '- Nama aplikasi',
  'help.document.line.step.2.text.11': '- ID saluran',
  'help.document.line.step.2.text.12': '- Rahasia saluran',
  'help.document.line.step.2.text.13': '- Token akses saluran (berlaku lama)',
  'help.document.line.step.2.text.14': '- ID akun resmi Line',
  'help.document.line.step.2.text.15':
    'Di halaman API pesan, klik untuk masuk ke pengembang Line',
  'help.document.line.step.2.text.16':
    'Tip: ID saluran dan rahasia saluran akan ditampilkan di halaman API pesan, dan Anda juga dapat menyalin dua informasi ini dari halaman ini',
  'help.document.line.step.2.text.17':
    'Setelah masuk ke pengembang Line, pertama klik avatar di sudut kanan atas, dan pilih akun Line',
  'help.document.line.step.2.text.18':
    'Dari bagian admin di sebelah kiri, pilih penyedia',
  'help.document.line.step.2.text.19':
    'Kemudian klik saluran (akun resmi) untuk masuk ke halaman pengaturan',
  'help.document.line.step.2.text.20':
    'Setelah masuk ke halaman pengaturan, dapatkan data yang diperlukan untuk koneksi dari tab "pengaturan dasar" dan "API pesan" masing-masing',
  'help.document.line.step.2.text.21':
    'Tiga item pertama dapat diperoleh di "pengaturan dasar"',
  'help.document.line.step.2.text.22': 'Beralih ke tab API pesan',
  'help.document.line.step.2.text.23':
    'Gulir ke bawah halaman, klik tombol keluaran untuk mendapatkan token akses saluran (berlaku lama)',
  'help.document.line.step.2.text.24':
    'Setelah mengklik, Anda dapat melihat informasi token akses saluran (berlaku lama)',
  'help.document.line.step.2.text.25': 'Cara mendapatkan ID akun resmi Line',
  'help.document.line.step.2.text.26':
    'Masuk ke backend akun resmi Line: <a>https://manager.line.biz</a>',
  'help.document.line.step.2.text.27':
    'Salin teks yang disorot merah pada gambar di bawah untuk mendapatkan ID Line dari akun resmi Line (tidak termasuk @)',
  'help.document.line.step.3.text':
    'Masuk ke platform ConnectNow dengan akun administrator, klik konfigurasi saluran->Line->tambah',
  'help.document.line.step.3.text.1': 'Klik tambah saluran',
  'help.document.line.step.3.text.2':
    'Setelah memasukkan nama saluran, klik selanjutnya, halaman berikut akan muncul, silakan tempel 5 data yang baru saja Anda peroleh ke kotak data yang sesuai.',
  'help.document.line.step.3.text.3':
    'Setelah menyelesaikan input, klik selanjutnya untuk melanjutkan dengan pengaturan terkait robot',
  'help.document.line.step.3.text.4': 'Dapatkan alamat webhook',
  'help.document.line.step.3.text.5':
    'Kembali ke tab API pesan pengembang Line',
  'help.document.line.step.3.text.6':
    'Temukan pengaturan webhook, dan klik "edit" di sini pada URL webhook',
  'help.document.line.step.3.text.7':
    'Tempel URL webhook Line yang baru saja disalin dari backend ConnectNow, klik "perbarui"',
  'help.document.line.step.3.text.8':
    'Tekan "Verifikasi" untuk memastikan Anda melihat "Berhasil",',
  'help.document.line.step.3.text.9':
    'Catatan: pastikan untuk mengklik selesai di platform ConnectNow sebelum melanjutkan dengan "verifikasi", jika tidak akan mengakibatkan kegagalan.',
  'help.document.line.step.3.text.10': 'Buka sakelar gunakan webhook',
  'help.document.line.step.3.text.11':
    'Jika Anda tidak dapat mengaktifkan gunakan webhook di pengembang Line, Anda dapat menyesuaikannya di "pengaturan respons" backend Line OA, dan centang "aktifkan" di bagian webhook',
  'help.document.line.step.3.text.12':
    'Jika Anda tidak akan membalas pesan di backend asli Line OA, tetapi hanya di backend ConnectNow, silakan buat pengaturan berikut di backend Line OA:',
  'help.document.line.step.3.text.13':
    '- Matikan "sakelar obrolan"; aktifkan webhook',
  'help.document.line.step.3.text.14':
    'Jika Anda perlu membalas pesan pelanggan di backend Line OA, pastikan untuk membuat pengaturan berikut di "pengaturan respons" Line OA:',
  'help.document.line.step.3.text.15': '- Nyalakan "sakelar obrolan";',
  'help.document.line.step.3.text.16': '- Aktifkan webhook',
  'help.document.line.step.3.text.17':
    '- Matikan "pesan selamat datang untuk menambah teman" (untuk menghindari memicu fungsi pesan selamat datang di Line OA dan ConnectNow secara bersamaan)',
  'help.document.line.step.3.text.18':
    '- Matikan "waktu respons" (untuk menghindari memicu fungsi pesan instan offline di Line OA dan ConnectNow secara bersamaan)',
  'help.document.line.step.3.text.19':
    '- Untuk "metode respons obrolan", pastikan untuk memilih "manual" (untuk menghindari memicu respons bot, balasan otomatis kata kunci, dan pesan selamat datang di Line OA dan ConnectNow secara bersamaan)',
  'help.document.line.step.3.text.20':
    'Selesai! Line Anda telah berhasil diintegrasikan ke ConnectNow',
  // Dokumentasi bantuan akun resmi WeChat
  'help.document.we.chat.official.left.menu':
    'Masuk ke platform resmi WeChat',
  'help.document.we.chat.official.left.menu.1':
    'Dapatkan ID akun resmi WeChat',
  'help.document.we.chat.official.left.menu.2':
    'Konfigurasi saluran ConnectNow',
  'help.document.we.chat.official.step.1.text':
    'Masuk ke platform resmi WeChat <a>pergi ke platform resmi</a>',
  'help.document.we.chat.official.step.1.text.1':
    'Buka WeChat di ponsel Anda, klik "+" di sudut kanan atas, pilih "pindai", setelah memindai kode QR, pilih organisasi untuk masuk, dan platform resmi akan secara otomatis mengarahkan ulang',
  'help.document.we.chat.official.step.2.text':
    'Untuk mendapatkan ID akun resmi, pilih pengaturan dan pengembangan->pengaturan akun resmi dari menu kiri, gulir ke bawah untuk menemukan ID asli',
  'help.document.we.chat.official.step.2.text.1':
    'Dapatkan APP ID, rahasia aplikasi, pilih pengaturan dan pengembangan->konfigurasi dasar dari menu kiri',
  'help.document.we.chat.official.step.3.text':
    'Dapatkan parameter melalui konfigurasi saluran ConnectNow',
  'help.document.we.chat.official.step.3.text.1':
    'Isi konfigurasi yang diperoleh ke platform resmi, klik ubah konfigurasi',
  'help.document.we.chat.official.step.3.text.2':
    'Konfigurasikan parameter yang diperoleh di langkah 4 konfigurasi saluran ConnectNow ke parameter yang sesuai',
  'help.document.we.chat.official.step.3.text.3': '',
  // Dokumentasi bantuan WhatsApp
  'help.document.whats.app.left.menu':
    'Materi yang harus disiapkan sebelum memulai',
  'help.document.whats.app.left.menu.1': 'Buat akun baru',
  'help.document.whats.app.left.menu.18': 'Klik buat entri',
  'help.document.whats.app.left.menu.2': 'Masuk ke Facebook',
  'help.document.whats.app.left.menu.3': 'Konfirmasi persetujuan',
  'help.document.whats.app.left.menu.4': 'Buat akun BM',
  'help.document.whats.app.left.menu.5': 'Buat WABA',
  'help.document.whats.app.left.menu.6': 'Atur informasi WABA dan nomor',
  'help.document.whats.app.left.menu.7': 'Ikat nomor ponsel',
  'help.document.whats.app.left.menu.8': 'Validasi kode verifikasi',
  'help.document.whats.app.left.menu.9': 'Konfirmasi kedua',
  'help.document.whats.app.left.menu.10': 'Pembuatan berhasil',
  'help.document.whats.app.left.menu.11': 'Pengikatan berhasil',
  'help.document.whats.app.left.menu.12': 'Selesaikan penambahan saluran',
  'help.document.whats.app.left.menu.13': 'Verifikasi bisnis',
  'help.document.whats.app.left.menu.14': 'Persiapan verifikasi bisnis',
  'help.document.whats.app.left.menu.15': 'Unggah dokumen pendukung',
  'help.document.whats.app.left.menu.16': 'Pilih metode kontak',
  'help.document.whats.app.left.menu.17': 'Tunggu hasil verifikasi',
  'help.document.whats.app.step.1.table.title': 'Siapkan materi',
  'help.document.whats.app.step.1.table.title.1': 'Contoh',
  'help.document.whats.app.step.1.table.title.2': 'Persyaratan khusus',
  'help.document.whats.app.step.1.table.body': 'Akun pribadi Facebook',
  'help.document.whats.app.step.1.table.body.1': '-',
  'help.document.whats.app.step.1.table.body.2':
    'Akun lama yang terdaftar setidaknya satu bulan (sebelum memulai, Anda dapat mengklik tautan untuk memeriksa apakah akun normal), digunakan untuk membuat manajer bisnis meta (singkatan BM)',
  'help.document.whats.app.step.1.table.body.3': 'Nomor ponsel',
  'help.document.whats.app.step.1.table.body.4': '+1 12035948979',
  'help.document.whats.app.step.1.table.body.5':
    'Harus dapat menerima SMS kode verifikasi; nomor ini tidak boleh pernah terdaftar sebelumnya untuk aplikasi WhatsApp atau akun bisnis (jika sebelumnya terdaftar untuk akun pribadi WhatsApp, akun pribadi WhatsApp harus dibatalkan sebelum digunakan); digunakan untuk membuat akun bisnis WhatsApp.',
  'help.document.whats.app.step.1.table.body.5.1':
    'Catatan: nomor ponsel daratan Tiongkok juga dapat diterima',
  'help.document.whats.app.step.1.table.body.6':
    'Nama tampilan bisnis WhatsApp',
  'help.document.whats.app.step.1.table.body.7': 'ConnectNow',
  'help.document.whats.app.step.1.table.body.8':
    'Harus terkait dengan situs web resmi merek dan digunakan untuk menamai akun bisnis WhatsApp',
  'help.document.whats.app.step.1.table.body.9': 'Nama perusahaan',
  'help.document.whats.app.step.1.table.body.10': 'ConnectNow',
  'help.document.whats.app.step.1.table.body.11':
    'Harus persis sama dengan nama perusahaan pada izin usaha atau sertifikat registrasi',
  'help.document.whats.app.step.1.table.body.12': 'Alamat perusahaan',
  'help.document.whats.app.step.1.table.body.13':
    '7500a beach road #04-307 the plaza singapore 19959',
  'help.document.whats.app.step.1.table.body.14':
    'Harus persis sama dengan alamat pada izin usaha atau sertifikat registrasi',
  'help.document.whats.app.step.1.table.body.15':
    'Izin usaha atau sertifikat registrasi',
  'help.document.whats.app.step.1.table.body.16': 'Business_profile.pdf',
  'help.document.whats.app.step.1.table.body.17':
    'Izin usaha atau dokumen registrasi yang sesuai dengan perusahaan',
  'help.document.whats.app.step.1.table.body.18': 'Situs web merek',
  'help.document.whats.app.step.1.table.body.19': 'www.connectnowai.com',
  'help.document.whats.app.step.1.table.body.20':
    '1.URL harus dienkripsi HTTPS;',
  'help.document.whats.app.step.1.table.body.20.1':
    '2.Konten URL harus dengan jelas mengekspresikan bisnis perusahaan;',
  'help.document.whats.app.step.1.table.body.20.2':
    '3.bagian bawah URL harus menyertakan nama perusahaan dan alamat, misalnya: hak cipta @ xxxx (tahun saat ini) +nama perusahaan semua hak dilindungi',
  'help.document.whats.app.step.1.table.body.21': 'Alamat email perusahaan',
  'help.document.whats.app.step.1.table.body.22': '<EMAIL>',
  'help.document.whats.app.step.1.table.body.23':
    'Akhiran email perusahaan harus konsisten dengan domain situs web merek. misalnya: www.connectnowai.<NAME_EMAIL> memenuhi persyaratan dan akan digunakan untuk menerima email kode verifikasi sekali pakai selama verifikasi bisnis',
  'help.document.whats.app.step.1.text':
    'Catatan: jika Anda sudah memiliki akun BM (manajer bisnis Facebook) dan ingin membuat akun API WhatsApp di bawah akun BM yang ada, Anda dapat masuk ke akun Facebook dari administrator akun BM tersebut di langkah 2 dari proses berikut, dan pilih akun BM yang ada di langkah 4.',
  'help.document.whats.app.step.2.text':
    'Setelah menyiapkan materi di atas, Anda dapat masuk ke ConnectNow sebagai administrator, klik konfigurasi saluran->WhatsApp->tambah->ikat WhatsApp di menu kiri untuk memulai proses registrasi tertanam',
  'help.document.whats.app.step.2.text.1':
    'Klik "konfigurasi saluran", gulir ke bawah untuk menemukan WhatsApp, klik "mulai konfigurasi"',
  'help.document.whats.app.step.2.text.2':
    'Masuk ke antarmuka "konfigurasi saluran WhatsApp", klik "tambah saluran"',
  'help.document.whats.app.step.2.text.3':
    'Masuk ke halaman "tambah saluran WhatsApp", klik ikat akun WhatsApp, masuk ke halaman login Facebook',
  'help.document.whats.app.step.2.text.4':
    'Setelah mengklik ikat "akun WhatsApp", pop-up otorisasi Facebook akan muncul. silakan masuk dengan akun Facebook yang telah disiapkan. jika akun salah, Anda dapat mengklik "masuk akun lain" untuk beralih. setelah mengonfirmasi akun sudah benar, klik lanjutkan untuk melanjutkan ke langkah berikutnya.',
  'help.document.whats.app.step.2.text.5':
    'Setelah mengonfirmasi kebenaran, klik mulai untuk melanjutkan pembuatan.',
  'help.document.whats.app.step.2.text.6': 'Masukkan informasi perusahaan Anda:',
  'help.document.whats.app.step.2.text.7':
    '*Nama perusahaan: nama perusahaan harus persis sama dengan nama pada izin usaha. jangan gunakan singkatan atau nama merek.',
  'help.document.whats.app.step.2.text.8':
    '*Email perusahaan: disarankan menggunakan alamat email dengan domain situs web yang sama.',
  'help.document.whats.app.step.2.text.9':
    '*Situs web perusahaan atau beranda bisnis: URL perusahaan. perhatikan bahwa URL perusahaan harus protokol HTTPS.',
  'help.document.whats.app.step.2.text.10':
    '*Negara/wilayah: negara operasi perusahaan. pastikan untuk memilih negara afiliasi untuk perusahaan yang ingin Anda sertifikasi. misalnya, jika Anda mengirimkan izin usaha Indonesia untuk verifikasi bisnis, maka pilih Indonesia sebagai negara.',
  'help.document.whats.app.step.2.text.11':
    'Catatan: jika Anda sudah memiliki akun BM, Anda dapat memilihnya di opsi portofolio bisnis.',
  'help.document.whats.app.step.2.text.12':
    'Pilih/buat WABA baru. jika WABA yang ada tersedia, Anda dapat memilihnya dari dropdown, jika tidak pilih buat.',
  'help.document.whats.app.step.2.text.13':
    '*Nama akun bisnis WhatsApp: nama WABA. digunakan untuk diferensiasi bisnis internal, audiens Anda tidak akan melihat informasi ini di profil akun WhatsApp Anda.',
  'help.document.whats.app.step.2.text.14':
    '*Nama tampilan WhatsApp: nama nomor. nama yang akhirnya dilihat oleh pelanggan, harus terkait dengan nama perusahaan, atau dengan nama merek. klik untuk melihat panduan nama tampilan.',
  'help.document.whats.app.step.2.text.15': '*Kategori: industri',
  'help.document.whats.app.step.2.text.16':
    'Masukkan nomor ponsel untuk mendaftarkan akun API WhatsApp, pilih metode untuk mendapatkan kode verifikasi:',
  'help.document.whats.app.step.2.text.17': '1. SMS',
  'help.document.whats.app.step.2.text.18': '2. panggilan suara',
  'help.document.whats.app.step.2.text.19':
    'Setelah menyelesaikan input, klik selanjutnya.',
  'help.document.whats.app.step.2.text.20':
    'Tip: untuk wilayah daratan Tiongkok (+86), disarankan menggunakan panggilan suara untuk menerima kode verifikasi.',
  'help.document.whats.app.step.2.text.21':
    'Masukkan kode verifikasi yang diterima dan klik selanjutnya',
  'help.document.whats.app.step.2.text.22':
    'Konfirmasi konten pembuatan Anda, klik lanjutkan jika benar',
  'help.document.whats.app.step.2.text.23':
    'Pesan pembuatan berhasil akan diminta, pastikan untuk mengklik tombol selesai di bawah pop-up.',
  'help.document.whats.app.step.2.text.24':
    'Ketika pop-up ditutup, ConnectNow akan melanjutkan dengan pengikatan.',
  'help.document.whats.app.step.2.text.25': 'Pilih nomor',
  'help.document.whats.app.step.2.text.26': 'Atur robot',
  'help.document.whats.app.step.2.text.27':
    'Setelah memasukkan nama saluran dan mengklik selesai, registrasi akun bisnis WhatsApp selesai',
  'help.document.whats.app.step.3.text':
    'Siapkan dan verifikasi akun BM Anda untuk mengirim pesan tanpa batas di WhatsApp setiap hari.',
  'help.document.whats.app.step.3.text.1':
    'Verifikasi bisnis BM adalah proses yang bertujuan untuk memverifikasi apakah akun BM milik organisasi nyata.',
  'help.document.whats.app.step.3.text.2':
    'Jika Anda belum menyelesaikan verifikasi bisnis BM, Anda akan tunduk pada pembatasan saat menggunakan API bisnis WhatsApp, termasuk:',
  'help.document.whats.app.step.3.text.3':
    'Mengirim percakapan yang dimulai bisnis kepada 250 pelanggan unik dalam periode 24 jam bergilir per nomor telepon.',
  'help.document.whats.app.step.3.text.4':
    'Mendaftarkan maksimal 2 nomor telepon.',
  'help.document.whats.app.step.3.text.5':
    'Setelah menyelesaikan verifikasi bisnis dan tinjauan nama tampilan, bisnis Anda akan memiliki kesempatan untuk dengan cepat mengangkat pembatasan:',
  'help.document.whats.app.step.3.text.6':
    'Memperluas percakapan yang dimulai bisnis kepada lebih banyak pelanggan: dimulai dari 1.000 pelanggan unik dalam periode 24 jam bergilir, secara bertahap meningkat menjadi 10.000, 100.000, atau tanpa batas per nomor telepon.',
  'help.document.whats.app.step.3.text.7':
    'Merespons percakapan yang dimulai pelanggan tanpa batas.',
  'help.document.whats.app.step.3.text.8':
    'Meminta untuk menjadi akun bisnis resmi (OBA).',
  'help.document.whats.app.step.3.text.9':
    'Mendaftarkan nomor telepon tambahan (maksimal 20 per BM).',
  'help.document.whats.app.step.3.text.10':
    'Pada April 2024, setelah memenuhi standar kualitas pesan dan menyelesaikan tinjauan nama tampilan, bisnis dapat menampilkan nama mereka dalam obrolan, meningkatkan kepercayaan pelanggan, tanpa memerlukan verifikasi bisnis. silakan merujuk pada dokumentasi meta:',
  'help.document.whats.app.step.3.text.11':
    'https://developers.facebook.com/docs/whatsapp/messaging-limits#open-1k-conversations-in-30-days',
  'help.document.whats.app.step.3.text.12':
    'Untuk meningkatkan peluang perusahaan Anda lulus verifikasi meta, Anda perlu mengonfirmasi informasi berikut terlebih dahulu:',
  'help.document.whats.app.step.3.text.13':
    'Alamat situs web resmi perusahaan: dan dienkripsi HTTPS, dan berisi nama perusahaan, alamat, atau nomor telepon',
  'help.document.whats.app.step.3.text.14':
    'Alamat email dengan domain yang sama dengan situs web perusahaan: akan digunakan untuk menerima email verifikasi sekali pakai (tidak diperlukan untuk verifikasi domain atau verifikasi nomor ponsel, tetapi umumnya, kami merekomendasikan verifikasi email)',
  'help.document.whats.app.step.3.text.15':
    'Dokumen resmi yang berisi nama hukum perusahaan: seperti izin usaha, anggaran dasar, atau sertifikat registrasi pajak bisnis',
  'help.document.whats.app.step.3.text.16':
    'Anda perlu memastikan bahwa nama perusahaan yang termasuk dalam dokumen relevan dengan situs web resmi perusahaan, misalnya, dengan memasukkan "milik perusahaan ABC" di footer',
  'help.document.whats.app.step.3.text.17': 'Proses verifikasi',
  'help.document.whats.app.step.3.text.17.1': '1. proses verifikasi',
  'help.document.whats.app.step.3.text.18':
    'Jika Anda adalah administrator akun manajer bisnis meta (setelah menyelesaikan bab 2, Anda akan secara otomatis menjadi administrator akun manajer bisnis meta), Anda dapat mengikuti langkah-langkah ini untuk mulai memverifikasi bisnis Anda:',
  'help.document.whats.app.step.3.text.19':
    'Pergi ke bagian <a>"pusat keamanan"</a> dari platform manajer bisnis.',
  'help.document.whats.app.step.3.text.20':
    'Jika Anda tidak melihat tombol "verifikasi", silakan kunjungi platform ConnectNow dan selesaikan proses registrasi tertanam (yaitu bab 2).',
  'help.document.whats.app.step.3.text.21':
    '2. kirimkan informasi dasar organisasi',
  'help.document.whats.app.step.3.text.22':
    'Berikan nama organisasi, alamat, nomor telepon, dan situs web Anda',
  'help.document.whats.app.step.3.text.23': 'Praktik terbaik',
  'help.document.whats.app.step.3.text.24':
    'Nama perusahaan/alamat yang Anda masukkan harus konsisten dengan nama/alamat pada dokumen pendukung.',
  'help.document.whats.app.step.3.text.25':
    'Nomor telepon dapat berupa nomor ponsel pribadi (tetapi perusahaan tidak dapat diverifikasi menggunakan nomor ponsel di langkah selanjutnya)',
  'help.document.whats.app.step.3.text.26':
    'Situs web yang dikirimkan harus berisi teks yang membuktikan kepemilikan domain, misalnya, dengan memasukkan "milik perusahaan ABC" di footer, dan nama perusahaan ini harus konsisten dengan nama perusahaan yang Anda masukkan.',
  'help.document.whats.app.step.3.text.27':
    'Setelah unggahan selesai, instal informasi halaman dan kirimkan untuk verifikasi',
  'help.document.whats.app.step.3.text.28': 'Praktik terbaik',
  'help.document.whats.app.step.3.text.29':
    'Verifikasi email lebih disukai, tetapi akhiran alamat email Anda harus cocok dengan domain yang dikirimkan (www.mypage.com >> <EMAIL>).',
  'help.document.whats.app.step.3.text.30':
    'Verifikasi domain adalah opsi berikutnya untuk memverifikasi bisnis Anda.',
  'help.document.whats.app.step.3.text.31':
    'Setelah memasukkan kode verifikasi, klik selanjutnya hingga langkah terakhir untuk mengirimkan verifikasi',
  'help.document.whats.app.step.3.text.32':
    'Setelah mengirimkan verifikasi, keputusan dapat dibuat secepat 10 menit, atau hingga 14 hari kerja. Anda akan diberi tahu setelah tinjauan selesai. jika Anda menerima konfirmasi bahwa Anda telah diverifikasi, tidak diperlukan tindakan lebih lanjut.',
};
