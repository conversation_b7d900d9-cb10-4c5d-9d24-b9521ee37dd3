export default {

    'marketing.results.activity.name.placeholder': '<PERSON><PERSON><PERSON> pilih nama kampanye',
    'marketing.results.marketing.channel.type': '<PERSON><PERSON>:',
    'marketing.results.marketing.channel.type.placeholder': '<PERSON><PERSON>an pilih jenis saluran pemasaran',
    'marketing.results.marketing.event.name': '<PERSON><PERSON>:',
    'marketing.results.marketing.event.name.placeholder': '<PERSON>lakan pilih nama acara pemasaran',
    'marketing.results.table.activity.name': '<PERSON><PERSON> kampanye',
    'marketing.results.table.marketing.channel.type': '<PERSON>is saluran',
    'marketing.results.table.send.channel': 'Saluran pengiriman',
    'marketing.results.table.marketing.events': 'Nama acara',
    'marketing.results.table.marketing.methods': 'Metode pemasaran',
    'marketing.results.table.test.type': 'Jenis <PERSON> A/B',
    'marketing.results.table.marketing.event.batches': 'Batch Acara',
    'marketing.results.table.number.customers': '<PERSON><PERSON><PERSON> pelanggan',
    'marketing.results.table.delivery.rate': 'Tingkat pengiriman',
    'marketing.results.table.read.rate': 'Tingkat buka',
    'marketing.results.table.click.rate': 'Tingkat klik',
    'marketing.results.table.subscription.rate': 'Tingkat berlangganan',
    'marketing.results.table.unsubscribe.rate': 'Tingkat berhenti berlangganan',
    'marketing.results.table.complaint.rate': 'Tingkat keluhan',
    'marketing.results.table.complaint.rate1': 'Tingkat keluhan',
    'marketing.results.table.failure.rate': 'Tingkat kegagalan',
    'marketing.results.table.bounce.rate': 'Tingkat pantulan',
    'marketing.results.table.delivery.delay.rate': 'Tingkat penundaan pengiriman',
    'marketing.results.table.reject.rate': 'Tingkat penolakan',
    'marketing.results.table.rendering.failure.rate': 'Tingkat kegagalan rendering',
    'marketing.results.table.operation': 'Operasi',
    'marketing.results.table.export.customer': 'Ekspor daftar pelanggan',
    'marketing.results.table.customer.list': 'Daftar Pelanggan',
    'marketing.results.table.marketing.event.sending.time': 'Waktu pengiriman acara pemasaran',
    'marketing.results.marketing.event.batches': 'Batch Acara:',
    'marketing.results.marketing.event.batches.placeholder': 'Silakan pilih batch acara pemasaran',
    'marketing.results.marketing.methods': `Metode pemasaran: `,
    'marketing.results.marketing.methods.1': 'Uji standar',
    'marketing.results.marketing.methods.2': 'Uji A/B',
    'marketing.results.marketing.methods.3': 'Rencana A',
    'marketing.results.marketing.methods.4': 'Rencana B',
    'marketing.results.marketing.methods.placeholder': 'Silakan pilih metode',
    'marketing.results.contact.information': 'Informasi Kontak:',
    'marketing.results.contact.information.placeholder': 'Silakan masukkan informasi kontak',
    'marketing.results.table.customer.name': 'Nama Pelanggan',
    'marketing.results.table.customer.contact.information': 'Informasi kontak pelanggan',
    'marketing.results.table.status': 'Status',
    'marketing.results.table.failure.reason': 'Alasan kegagalan',
    'marketing.results.table.detail': 'Detail',
    'event.notification.status.service': 'Pengiriman',
    'event.notification.status.read': 'Buka',
    'event.notification.status.click': 'Klik',
    'event.notification.status.subscribe': 'Berlangganan',
    'event.notification.status.unsubscribe': 'Berhenti Berlangganan',
    'event.notification.status.fail': 'Kegagalan Rendering',
    'event.notification.status.complaint': 'Keluhan',
    'event.notification.status.have.send': 'Terkirim',
    'event.notification.status.bounce': 'Pantulan',
    'event.notification.status.reject': 'Ditolak',
    'event.notification.status.delivery.delay': 'Penundaan Pengiriman',
    'marketing.details.customer.information.title': 'Informasi Dasar Pelanggan',
    'marketing.details.activity.information.title': 'Informasi Dasar Kampanye',
    'marketing.details.customer.information.customer.name': 'Nama pelanggan: ',
    'marketing.details.customer.information.customer.phone': 'Nomor telepon pelanggan: ',
    'marketing.details.customer.information.customer.whats.app': 'Nomor WhatsApp: ',
    'marketing.details.customer.information.customer.email': 'Email pelanggan: ',
    'marketing.details.customer.information.marketing.result': 'Hasil pemasaran: ',
    'marketing.details.history.title': 'Rekaman historis',
    'marketing.channel.type.email': 'Email',
    'marketing.channel.type.all': 'Semua saluran',
    'marketing.channel.type.all.small': 'Semua saluran',
    'marketing.channel.type.phone': 'Telepon',
    'marketing.channel.type.whats.app': 'WhatsApp',
    'marketing.channel.type.info': 'SMS',
    'marketing.channel.type.chat': 'Web Chat',
    'marketing.channel.type.app.chat': 'App Chat',
    'marketing.channel.type.web.video': 'Video Langsung Web',
    'marketing.channel.type.app.video': 'Video Langsung Aplikasi',
    'marketing.channel.type.amazon.message': 'Pesan Amazon',
    'marketing.channel.type.facebook': 'Facebook Messenger',
    'marketing.channel.type.instagram': 'Instagram',
    'marketing.channel.type.line': 'Line',
    'marketing.channel.type.weCom': 'Layanan Pelanggan WeChat',
    'marketing.channel.type.weChat.official.account': 'Akun Resmi WeChat',
    'marketing.channel.type.web.online.video': 'Suara Online WEB',
    'marketing.channel.type.app.online.video': 'Suara Online APLIKASI',
    'marketing.channel.type.twitter': 'Twitter',
    'marketing.channel.type.telegram': 'Telegram',
    'marketing.channel.type.weChat.mini.program': 'Program Mini WeChat',
    'marketing.channel.type.shopify': 'Shopify',
    'marketing.channel.type.google.play': 'Google Play',
    'marketing.channel.type.discord': 'Discord',
    'test.analysis.result.title': 'Hasil Analisis',
    'test.analysis.result.comparison.dimension': 'Dimensi perbandingan:',
    'test.analysis.result.comparison.dimension.1': 'Audiens Target',
    'test.analysis.result.comparison.dimension.2': 'Konten Pemasaran',
    'test.analysis.result.comparison.dimension.3': 'Waktu Pemasaran',
    'test.analysis.result.overall.result': 'Hasil keseluruhan:',
    'marketing.results.table.delivery.rate.1': 'Tingkat pengiriman:',
    'marketing.results.table.read.rate.1': 'Tingkat buka:',
    'marketing.results.table.click.rate.1': 'Tingkat klik:',
    'marketing.results.table.subscription.rate.1': 'Tingkat berlangganan:',
    'marketing.results.table.unsubscribe.rate.1': 'Tingkat berhenti berlangganan:',
    'marketing.results.table.complaint.rate.1': 'Tingkat keluhan:',
    'marketing.results.table.failure.rate.1': 'Tingkat kegagalan:',
    'marketing.results.table.bounce.rate.1': 'Tingkat pantulan:',
    'marketing.results.table.delivery.delay.rate.1': 'Tingkat penundaan pengiriman:',
    'marketing.results.table.reject.rate.1': 'Tingkat penolakan:',
    'marketing.results.table.rendering.failure.rate.1': 'Tingkat kegagalan rendering:',
    'test.analysis.semicolon': ';',
    'test.analysis.period': '.',
    'marketing.channel.type.discord': 'Discord',
};
